[gd_scene load_steps=3 format=3 uid="uid://bh8x2ywqxqxr"]

[ext_resource type="Script" uid="uid://cjcy3yiirqptw" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxr" path="res://scenes/ui/MobileMenuButton.tscn" id="2_button"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="SafeAreaContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BackgroundLayer" type="Control" parent="SafeAreaContainer"]
layout_mode = 2

[node name="DynamicBackground" type="TextureRect" parent="SafeAreaContainer/BackgroundLayer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 6

[node name="OverlayGradient" type="ColorRect" parent="SafeAreaContainer/BackgroundLayer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.15, 0.3)

[node name="AtmosphericEffects" type="Node2D" parent="SafeAreaContainer/BackgroundLayer"]

[node name="FogParticles" type="GPUParticles2D" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]
position = Vector2(540, 100)
amount = 50
lifetime = 8.0

[node name="RavenAnimation" type="AnimatedSprite2D" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]
position = Vector2(200, 300)

[node name="LightningFlash" type="AnimationPlayer" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]

[node name="UILayer" type="Control" parent="SafeAreaContainer"]
layout_mode = 2

[node name="TopSection" type="VBoxContainer" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 768.0
grow_horizontal = 2
alignment = 1

[node name="GameTitle" type="RichTextLabel" parent="SafeAreaContainer/UILayer/TopSection"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 2
bbcode_enabled = true
text = "[center][font_size=48][color=#D4AF37]PREKLIATE[/color]
[color=#8B0000]DEDIČSTVO[/color][/font_size][/center]"
fit_content = true

[node name="TitleOrnament" type="TextureRect" parent="SafeAreaContainer/UILayer/TopSection"]
custom_minimum_size = Vector2(600, 100)
layout_mode = 2
stretch_mode = 4

[node name="Subtitle" type="Label" parent="SafeAreaContainer/UILayer/TopSection"]
layout_mode = 2
text = "Cursed Legacy"
horizontal_alignment = 1

[node name="MiddleSection" type="VBoxContainer" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="MenuFrame" type="NinePatchRect" parent="SafeAreaContainer/UILayer/MiddleSection"]
custom_minimum_size = Vector2(800, 600)
layout_mode = 2
patch_margin_left = 40
patch_margin_top = 40
patch_margin_right = 40
patch_margin_bottom = 40

[node name="ButtonContainer" type="VBoxContainer" parent="SafeAreaContainer/UILayer/MiddleSection"]
custom_minimum_size = Vector2(600, 500)
layout_mode = 2
alignment = 1

[node name="NovaHraButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
layout_mode = 2
button_text = "Nová Hra"
button_action = "new_game"

[node name="PokracovatButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
layout_mode = 2
button_text = "Pokračovať"
button_action = "continue_game"

[node name="NastaveniaButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
layout_mode = 2
button_text = "Nastavenia"
button_action = "settings"

[node name="GaleriaButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
layout_mode = 2
button_text = "Galéria"
button_action = "gallery"

[node name="UkoncitButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
layout_mode = 2
button_text = "Ukončiť"
button_action = "quit"

[node name="BottomSection" type="Control" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -192.0
grow_horizontal = 2
grow_vertical = 0

[node name="LoadingSpinner" type="TextureRect" parent="SafeAreaContainer/UILayer/BottomSection"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -64.0
offset_top = -64.0
offset_right = 64.0
offset_bottom = 64.0
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 4

[node name="VersionLabel" type="Label" parent="SafeAreaContainer/UILayer/BottomSection"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -30.0
grow_horizontal = 0
grow_vertical = 0
text = "v1.0.0"
horizontal_alignment = 2
