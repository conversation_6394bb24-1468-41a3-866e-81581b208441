extends Control

# UI References
@onready var safe_area_container: MarginContainer = $SafeAreaContainer
@onready var dynamic_background: TextureRect = $SafeAreaContainer/BackgroundLayer/DynamicBackground
@onready var overlay_gradient: ColorRect = $SafeAreaContainer/BackgroundLayer/OverlayGradient
@onready var fog_particles: GPUParticles2D = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/FogParticles
@onready var raven_animation: AnimatedSprite2D = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/RavenAnimation
@onready var lightning_flash: AnimationPlayer = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/LightningFlash

@onready var game_title: RichTextLabel = $SafeAreaContainer/UILayer/TopSection/GameTitle
@onready var title_ornament: TextureRect = $SafeAreaContainer/UILayer/TopSection/TitleOrnament
@onready var subtitle: Label = $SafeAreaContainer/UILayer/TopSection/Subtitle
@onready var menu_frame: NinePatchRect = $SafeAreaContainer/UILayer/MiddleSection/MenuFrame
@onready var button_container: VBoxContainer = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer

@onready var nova_hra_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/NovaHraButton
@onready var pokracovat_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/PokracovatButton
@onready var nastavenia_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/NastaveniaButton
@onready var galeria_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/GaleriaButton
@onready var ukoncit_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/UkoncitButton

@onready var loading_spinner: TextureRect = $SafeAreaContainer/UILayer/BottomSection/LoadingSpinner
@onready var version_label: Label = $SafeAreaContainer/UILayer/BottomSection/VersionLabel

# Loading state
var assets_loaded: bool = false
var loading_rotation_speed: float = 180.0  # degrees per second

func _ready():
	print("MainMenu: Initializing...")
	
	# Setup safe area
	_setup_safe_area()
	
	# Setup initial UI
	_setup_initial_ui()
	
	# Connect to asset manager
	_connect_asset_manager()
	
	# Setup button actions
	_setup_button_actions()
	
	# Start asset generation
	_start_asset_loading()
	
	# Setup atmospheric effects
	_setup_atmospheric_effects()

func _setup_safe_area():
	if UIScaler:
		UIScaler.apply_safe_area_to_control(safe_area_container)

func _setup_initial_ui():
	# Setup title
	game_title.text = "[center][font_size=48][color=#D4AF37]PREKLIATE[/color]\n[color=#8B0000]DEDIČSTVO[/color][/font_size][/center]"
	subtitle.text = "Cursed Legacy"
	
	# Setup version
	version_label.text = "v1.0.0"
	
	# Setup overlay gradient for atmospheric effect
	overlay_gradient.color = Color(0.1, 0.1, 0.15, 0.3)
	
	# Initially hide loading spinner
	loading_spinner.visible = false

func _connect_asset_manager():
	if AssetManager:
		AssetManager.asset_generated.connect(_on_asset_generated)
		AssetManager.all_assets_loaded.connect(_on_all_assets_loaded)
		AssetManager.loading_progress.connect(_on_loading_progress)

func _setup_button_actions():
	# Setup button texts and actions
	nova_hra_button.button_text = "Nová Hra"
	nova_hra_button.button_action = "new_game"
	nova_hra_button.pressed.connect(func(): _on_button_action("new_game"))
	
	pokracovat_button.button_text = "Pokračovať"
	pokracovat_button.button_action = "continue_game"
	pokracovat_button.pressed.connect(func(): _on_button_action("continue_game"))
	pokracovat_button.set_enabled(false)  # Disabled until save exists
	
	nastavenia_button.button_text = "Nastavenia"
	nastavenia_button.button_action = "settings"
	nastavenia_button.pressed.connect(func(): _on_button_action("settings"))
	
	galeria_button.button_text = "Galéria"
	galeria_button.button_action = "gallery"
	galeria_button.pressed.connect(func(): _on_button_action("gallery"))
	
	ukoncit_button.button_text = "Ukončiť"
	ukoncit_button.button_action = "quit"
	ukoncit_button.pressed.connect(func(): _on_button_action("quit"))

func _start_asset_loading():
	print("MainMenu: Starting asset loading...")
	loading_spinner.visible = true
	
	if AssetManager:
		AssetManager.generate_all_mobile_assets()
	else:
		print("AssetManager not available!")
		_on_all_assets_loaded()  # Fallback

func _setup_atmospheric_effects():
	# Setup fog particles
	if fog_particles:
		_setup_fog_particles()
	
	# Setup lightning animation
	if lightning_flash:
		_setup_lightning_animation()

func _setup_fog_particles():
	# Configure fog particles for mobile performance
	fog_particles.emitting = true
	fog_particles.amount = 50  # Reduced for mobile
	fog_particles.lifetime = 8.0
	
	# Setup particle material
	var material = ParticleProcessMaterial.new()
	material.direction = Vector3(1, -0.5, 0)
	material.initial_velocity_min = 20.0
	material.initial_velocity_max = 40.0
	material.gravity = Vector3(0, -10, 0)
	material.scale_min = 0.5
	material.scale_max = 1.5
	material.color = Color(0.8, 0.8, 0.9, 0.3)
	
	fog_particles.process_material = material

func _setup_lightning_animation():
	# Create lightning flash animation
	var animation = Animation.new()
	animation.length = 0.3
	
	# Flash the overlay
	var track = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track, "SafeAreaContainer/BackgroundLayer/OverlayGradient:color")
	animation.track_insert_key(track, 0.0, Color(0.1, 0.1, 0.15, 0.3))
	animation.track_insert_key(track, 0.1, Color(0.9, 0.9, 1.0, 0.8))
	animation.track_insert_key(track, 0.3, Color(0.1, 0.1, 0.15, 0.3))
	
	var library = AnimationLibrary.new()
	library.add_animation("lightning_flash", animation)
	lightning_flash.add_animation_library("default", library)
	
	# Random lightning flashes
	_schedule_random_lightning()

func _schedule_random_lightning():
	var delay = randf_range(10.0, 30.0)  # Random delay between 10-30 seconds
	await get_tree().create_timer(delay).timeout
	
	if lightning_flash and is_inside_tree():
		lightning_flash.play("default/lightning_flash")
		_schedule_random_lightning()  # Schedule next flash

func _process(delta):
	# Rotate loading spinner
	if loading_spinner.visible:
		loading_spinner.rotation_degrees += loading_rotation_speed * delta

func _on_asset_generated(asset_type: String, texture: ImageTexture):
	print("MainMenu: Asset generated: ", asset_type)
	
	match asset_type:
		"main_background":
			if dynamic_background:
				dynamic_background.texture = texture
		"menu_frame":
			if menu_frame:
				menu_frame.texture = texture
		"title_ornament":
			if title_ornament:
				title_ornament.texture = texture
		"loading_spinner":
			if loading_spinner:
				loading_spinner.texture = texture
		"raven_silhouette":
			if raven_animation:
				# Create simple animation from static texture
				_create_raven_animation(texture)

func _create_raven_animation(texture: ImageTexture):
	# Create a simple bobbing animation for the raven
	var tween = create_tween()
	tween.set_loops()
	tween.tween_property(raven_animation, "position:y", raven_animation.position.y - 10, 2.0)
	tween.tween_property(raven_animation, "position:y", raven_animation.position.y + 10, 2.0)
	
	# Set the texture
	var sprite_frames = SpriteFrames.new()
	sprite_frames.add_animation("default")
	sprite_frames.add_frame("default", texture)
	raven_animation.sprite_frames = sprite_frames
	raven_animation.play("default")

func _on_loading_progress(current: int, total: int, asset_type: String):
	print("MainMenu: Loading progress: ", current, "/", total, " (", asset_type, ")")
	# Could update a progress bar here if desired

func _on_all_assets_loaded():
	print("MainMenu: All assets loaded!")
	assets_loaded = true
	loading_spinner.visible = false
	
	# Enable continue button if save exists
	_check_save_file()
	
	# Start intro animation
	_play_intro_animation()

func _check_save_file():
	if FileAccess.file_exists("user://savegame.dat"):
		pokracovat_button.set_enabled(true)

func _play_intro_animation():
	# Fade in animation for UI elements
	var ui_elements = [game_title, subtitle, title_ornament, button_container]
	
	for i in range(ui_elements.size()):
		var element = ui_elements[i]
		element.modulate.a = 0.0
		
		var tween = create_tween()
		tween.tween_delay(i * 0.2)
		tween.tween_property(element, "modulate:a", 1.0, 0.5)

func _on_button_action(action: String):
	print("MainMenu: Button action: ", action)
	
	match action:
		"new_game":
			_start_new_game()
		"continue_game":
			_continue_game()
		"settings":
			_open_settings()
		"gallery":
			_open_gallery()
		"quit":
			_quit_game()

func _start_new_game():
	print("Starting new game...")
	# TODO: Transition to game scene
	# get_tree().change_scene_to_file("res://scenes/GameIntro.tscn")

func _continue_game():
	print("Continuing game...")
	# TODO: Load save and transition to game
	# GameManager.load_game()
	# get_tree().change_scene_to_file("res://scenes/GameWorld.tscn")

func _open_settings():
	print("Opening settings...")
	# TODO: Open settings menu
	# get_tree().change_scene_to_file("res://scenes/SettingsMenu.tscn")

func _open_gallery():
	print("Opening gallery...")
	# TODO: Open gallery/achievements
	# get_tree().change_scene_to_file("res://scenes/Gallery.tscn")

func _quit_game():
	print("Quitting game...")
	get_tree().quit()
