extends Node

signal performance_mode_changed(mode: String)
signal memory_warning()

enum PerformanceMode {
	HIGH,
	MEDIUM,
	LOW,
	BATTERY_SAVER
}

var current_performance_mode: PerformanceMode = PerformanceMode.HIGH
var memory_usage_mb: float = 0.0
var battery_level: float = 1.0
var is_low_power_mode: bool = false

# Performance thresholds
const MEMORY_WARNING_THRESHOLD_MB = 150.0
const BATTERY_LOW_THRESHOLD = 0.2
const PERFORMANCE_CHECK_INTERVAL = 5.0

var performance_timer: Timer

func _ready():
	print("MobilePerformanceManager: Initializing...")
	
	# Setup performance monitoring timer
	performance_timer = Timer.new()
	performance_timer.wait_time = PERFORMANCE_CHECK_INTERVAL
	performance_timer.timeout.connect(_check_performance)
	performance_timer.autostart = true
	add_child(performance_timer)
	
	# Initial performance assessment
	_assess_device_capabilities()
	_apply_initial_optimizations()

func _assess_device_capabilities():
	"""Assess device capabilities and set initial performance mode"""
	
	# Get device info
	var device_name = OS.get_model_name()
	var processor_count = OS.get_processor_count()
	
	print("Device: ", device_name, " Processors: ", processor_count)
	
	# Estimate performance based on available info
	if processor_count >= 8:
		current_performance_mode = PerformanceMode.HIGH
	elif processor_count >= 4:
		current_performance_mode = PerformanceMode.MEDIUM
	else:
		current_performance_mode = PerformanceMode.LOW
	
	# Check if running on older/weaker hardware
	if OS.has_feature("mobile") and processor_count < 4:
		current_performance_mode = PerformanceMode.LOW
	
	print("Initial performance mode: ", PerformanceMode.keys()[current_performance_mode])

func _apply_initial_optimizations():
	"""Apply initial performance optimizations based on device"""
	
	# Set rendering optimizations
	_apply_rendering_optimizations()
	
	# Set memory optimizations
	_apply_memory_optimizations()
	
	# Set particle optimizations
	_apply_particle_optimizations()
	
	# Set audio optimizations
	_apply_audio_optimizations()

func _apply_rendering_optimizations():
	"""Apply rendering optimizations based on performance mode"""
	
	var viewport = get_viewport()
	if not viewport:
		return
	
	match current_performance_mode:
		PerformanceMode.HIGH:
			# High quality settings
			viewport.msaa_2d = Viewport.MSAA_2X
			viewport.snap_2d_transforms_to_pixel = false
			viewport.snap_2d_vertices_to_pixel = false
		
		PerformanceMode.MEDIUM:
			# Medium quality settings
			viewport.msaa_2d = Viewport.MSAA_DISABLED
			viewport.snap_2d_transforms_to_pixel = true
			viewport.snap_2d_vertices_to_pixel = false
		
		PerformanceMode.LOW, PerformanceMode.BATTERY_SAVER:
			# Low quality settings for performance
			viewport.msaa_2d = Viewport.MSAA_DISABLED
			viewport.snap_2d_transforms_to_pixel = true
			viewport.snap_2d_vertices_to_pixel = true
			
			# Reduce render scale for very low-end devices
			if current_performance_mode == PerformanceMode.BATTERY_SAVER:
				viewport.scaling_3d_scale = 0.8

func _apply_memory_optimizations():
	"""Apply memory optimizations"""
	
	match current_performance_mode:
		PerformanceMode.HIGH:
			# Allow higher memory usage
			pass
		
		PerformanceMode.MEDIUM:
			# Moderate memory optimizations
			_optimize_texture_memory(0.8)
		
		PerformanceMode.LOW:
			# Aggressive memory optimizations
			_optimize_texture_memory(0.6)
			_enable_texture_compression()
		
		PerformanceMode.BATTERY_SAVER:
			# Maximum memory optimizations
			_optimize_texture_memory(0.4)
			_enable_texture_compression()
			_reduce_cache_sizes()

func _optimize_texture_memory(scale_factor: float):
	"""Optimize texture memory usage"""
	# This would typically involve reducing texture sizes
	# For now, we'll just log the optimization
	print("Optimizing texture memory with scale factor: ", scale_factor)

func _enable_texture_compression():
	"""Enable aggressive texture compression"""
	print("Enabling texture compression for memory optimization")

func _reduce_cache_sizes():
	"""Reduce cache sizes to save memory"""
	if AssetManager:
		# Limit asset cache size
		print("Reducing asset cache size for battery saver mode")

func _apply_particle_optimizations():
	"""Optimize particle systems for performance"""
	
	var particle_scale = 1.0
	
	match current_performance_mode:
		PerformanceMode.HIGH:
			particle_scale = 1.0
		PerformanceMode.MEDIUM:
			particle_scale = 0.7
		PerformanceMode.LOW:
			particle_scale = 0.5
		PerformanceMode.BATTERY_SAVER:
			particle_scale = 0.3
	
	# Apply to all particle systems in the scene
	_scale_particle_systems(particle_scale)

func _scale_particle_systems(scale: float):
	"""Scale all particle systems in the scene"""
	var particles = get_tree().get_nodes_in_group("particles")
	for particle in particles:
		if particle is GPUParticles2D:
			var gpu_particle = particle as GPUParticles2D
			gpu_particle.amount = int(gpu_particle.amount * scale)

func _apply_audio_optimizations():
	"""Optimize audio settings for performance"""
	
	match current_performance_mode:
		PerformanceMode.HIGH:
			# High quality audio
			AudioServer.set_bus_effect_enabled(0, 0, true)  # Enable reverb if exists
		
		PerformanceMode.MEDIUM:
			# Medium quality audio
			pass
		
		PerformanceMode.LOW, PerformanceMode.BATTERY_SAVER:
			# Reduce audio quality for performance
			AudioServer.set_bus_effect_enabled(0, 0, false)  # Disable effects

func _check_performance():
	"""Periodically check performance and adjust settings"""
	
	_update_memory_usage()
	_update_battery_status()
	_check_frame_rate()
	
	# Adjust performance mode if needed
	var new_mode = _calculate_optimal_performance_mode()
	if new_mode != current_performance_mode:
		set_performance_mode(new_mode)

func _update_memory_usage():
	"""Update memory usage information"""
	# Godot doesn't provide direct memory usage API
	# This is a placeholder for memory monitoring
	memory_usage_mb = OS.get_static_memory_usage_by_type().values().reduce(func(a, b): return a + b, 0) / 1024.0 / 1024.0
	
	if memory_usage_mb > MEMORY_WARNING_THRESHOLD_MB:
		emit_signal("memory_warning")
		print("Memory warning: ", memory_usage_mb, " MB used")

func _update_battery_status():
	"""Update battery status if available"""
	if OS.has_feature("mobile"):
		# This would require platform-specific implementation
		# For now, we'll simulate battery monitoring
		pass

func _check_frame_rate():
	"""Check current frame rate and adjust if needed"""
	var fps = Engine.get_frames_per_second()
	
	if fps < 30 and current_performance_mode != PerformanceMode.BATTERY_SAVER:
		print("Low FPS detected: ", fps, " - considering performance reduction")
		
		# If FPS is consistently low, reduce performance mode
		if fps < 20:
			var lower_mode = min(current_performance_mode + 1, PerformanceMode.BATTERY_SAVER)
			set_performance_mode(lower_mode)

func _calculate_optimal_performance_mode() -> PerformanceMode:
	"""Calculate optimal performance mode based on current conditions"""
	
	var optimal_mode = current_performance_mode
	
	# Check battery level
	if battery_level < BATTERY_LOW_THRESHOLD:
		optimal_mode = PerformanceMode.BATTERY_SAVER
	
	# Check memory pressure
	if memory_usage_mb > MEMORY_WARNING_THRESHOLD_MB:
		optimal_mode = max(optimal_mode, PerformanceMode.LOW)
	
	# Check if device is in low power mode
	if is_low_power_mode:
		optimal_mode = PerformanceMode.BATTERY_SAVER
	
	return optimal_mode

func set_performance_mode(mode: PerformanceMode):
	"""Set performance mode and apply optimizations"""
	
	if mode == current_performance_mode:
		return
	
	print("Changing performance mode from ", PerformanceMode.keys()[current_performance_mode], " to ", PerformanceMode.keys()[mode])
	
	current_performance_mode = mode
	
	# Apply optimizations for new mode
	_apply_rendering_optimizations()
	_apply_memory_optimizations()
	_apply_particle_optimizations()
	_apply_audio_optimizations()
	
	emit_signal("performance_mode_changed", PerformanceMode.keys()[mode])

func get_performance_mode() -> PerformanceMode:
	"""Get current performance mode"""
	return current_performance_mode

func force_garbage_collection():
	"""Force garbage collection to free memory"""
	print("Forcing garbage collection...")
	# Godot automatically manages memory, but we can suggest cleanup
	get_tree().call_deferred("_flush_transform_notifications")

func optimize_for_battery():
	"""Optimize specifically for battery life"""
	set_performance_mode(PerformanceMode.BATTERY_SAVER)
	
	# Additional battery optimizations
	Engine.max_fps = 30  # Limit FPS to save battery
	
	# Reduce background processing
	get_tree().set_pause(false)  # Ensure we can still process

func restore_performance():
	"""Restore performance after battery optimization"""
	Engine.max_fps = 60  # Restore normal FPS
	_assess_device_capabilities()  # Re-assess optimal mode

func get_memory_usage_mb() -> float:
	"""Get current memory usage in MB"""
	return memory_usage_mb

func is_low_memory() -> bool:
	"""Check if device is in low memory state"""
	return memory_usage_mb > MEMORY_WARNING_THRESHOLD_MB

func get_performance_stats() -> Dictionary:
	"""Get performance statistics"""
	return {
		"performance_mode": PerformanceMode.keys()[current_performance_mode],
		"memory_usage_mb": memory_usage_mb,
		"fps": Engine.get_frames_per_second(),
		"battery_level": battery_level,
		"is_low_power_mode": is_low_power_mode
	}
