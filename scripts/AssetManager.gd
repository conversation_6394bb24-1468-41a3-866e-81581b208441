extends Node

signal asset_generated(asset_type: String, texture: ImageTexture)
signal all_assets_loaded()
signal loading_progress(current: int, total: int, asset_type: String)

var scenario_api: ScenarioAPI
var asset_cache: Dictionary = {}
var generation_queue: Array = []
var is_generating: bool = false
var current_generation_index: int = 0
var total_assets: int = 0

# Mobile asset specifications with priority
const MOBILE_ASSET_SPECS = {
	"main_background": {"width": 1080, "height": 1920, "priority": 1},
	"menu_frame": {"width": 800, "height": 1200, "priority": 2},
	"button_normal": {"width": 400, "height": 120, "priority": 3},
	"button_pressed": {"width": 400, "height": 120, "priority": 3},
	"title_ornament": {"width": 600, "height": 200, "priority": 2},
	"loading_spinner": {"width": 128, "height": 128, "priority": 4},
	"particle_fog": {"width": 64, "height": 64, "priority": 5},
	"raven_silhouette": {"width": 128, "height": 128, "priority": 5}
}

# Cache directory
const CACHE_DIR = "user://generated_assets/"

func _ready():
	# Create cache directory
	_ensure_cache_directory()
	
	# Initialize Scenario API
	scenario_api = ScenarioAPI.new()
	add_child(scenario_api)
	scenario_api.image_generated.connect(_on_asset_generated)
	scenario_api.generation_failed.connect(_on_generation_failed)
	scenario_api.generation_progress.connect(_on_generation_progress)
	
	# Load cached assets first
	_load_cached_assets()

func _ensure_cache_directory():
	var dir = DirAccess.open("user://")
	if not dir.dir_exists("generated_assets"):
		dir.make_dir_recursive("generated_assets")

func generate_all_mobile_assets():
	print("Starting mobile asset generation...")
	
	# Sort assets by priority
	var sorted_assets = MOBILE_ASSET_SPECS.keys()
	sorted_assets.sort_custom(func(a, b): return MOBILE_ASSET_SPECS[a].priority < MOBILE_ASSET_SPECS[b].priority)
	
	# Only generate assets that aren't cached
	generation_queue.clear()
	for asset_type in sorted_assets:
		if not asset_cache.has(asset_type):
			generation_queue.append(asset_type)
	
	total_assets = generation_queue.size()
	current_generation_index = 0
	
	if generation_queue.is_empty():
		print("All assets already cached!")
		emit_signal("all_assets_loaded")
		return
	
	print("Need to generate ", total_assets, " assets")
	_process_generation_queue()

func generate_asset(asset_type: String):
	if not MOBILE_ASSET_SPECS.has(asset_type):
		print("Unknown asset type: ", asset_type)
		return false
	
	if asset_cache.has(asset_type):
		print("Asset already cached: ", asset_type)
		emit_signal("asset_generated", asset_type, asset_cache[asset_type])
		return true
	
	var specs = MOBILE_ASSET_SPECS[asset_type]
	print("Generating asset: ", asset_type, " (", specs.width, "x", specs.height, ")")
	
	scenario_api.generate_mobile_asset(asset_type, specs.width, specs.height)
	return true

func _process_generation_queue():
	if generation_queue.is_empty():
		print("Generation queue empty, all assets loaded!")
		emit_signal("all_assets_loaded")
		return
	
	if is_generating:
		return
	
	is_generating = true
	var asset_type = generation_queue[0]
	
	emit_signal("loading_progress", current_generation_index + 1, total_assets, asset_type)
	
	if not generate_asset(asset_type):
		# Skip this asset and continue
		generation_queue.pop_front()
		current_generation_index += 1
		is_generating = false
		_process_generation_queue()

func _on_asset_generated(asset_type: String, texture: ImageTexture):
	print("Asset generated successfully: ", asset_type)
	
	asset_cache[asset_type] = texture
	
	# Save to cache
	_save_asset_to_cache(asset_type, texture)
	
	emit_signal("asset_generated", asset_type, texture)
	
	# Remove from queue and continue
	if generation_queue.has(asset_type):
		generation_queue.erase(asset_type)
		current_generation_index += 1
	
	is_generating = false
	
	# Continue with next asset
	await get_tree().create_timer(1.0).timeout  # Small delay between generations
	_process_generation_queue()

func _on_generation_failed(asset_type: String, error: String):
	print("Asset generation failed: ", asset_type, " - ", error)
	
	# Load fallback asset
	var fallback_texture = _load_fallback_asset(asset_type)
	if fallback_texture:
		asset_cache[asset_type] = fallback_texture
		emit_signal("asset_generated", asset_type, fallback_texture)
	
	# Remove from queue and continue
	if generation_queue.has(asset_type):
		generation_queue.erase(asset_type)
		current_generation_index += 1
	
	is_generating = false
	
	# Continue with next asset
	await get_tree().create_timer(0.5).timeout
	_process_generation_queue()

func _on_generation_progress(asset_type: String, progress: float):
	# Update loading progress for current asset
	pass

func get_asset(asset_type: String) -> ImageTexture:
	if asset_cache.has(asset_type):
		return asset_cache[asset_type]
	else:
		# Return fallback asset
		var fallback = _load_fallback_asset(asset_type)
		if fallback:
			return fallback
		else:
			return _create_placeholder_texture(asset_type)

func _save_asset_to_cache(asset_type: String, texture: ImageTexture):
	var image = texture.get_image()
	var path = CACHE_DIR + asset_type + ".png"
	
	var error = image.save_png(path)
	if error == OK:
		print("Asset cached: ", path)
	else:
		print("Failed to cache asset: ", asset_type, " - Error: ", error)

func _load_cached_assets():
	print("Loading cached assets...")
	var dir = DirAccess.open(CACHE_DIR)
	if not dir:
		print("Cache directory not accessible")
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".png"):
			var asset_type = file_name.get_basename()
			if MOBILE_ASSET_SPECS.has(asset_type):
				var texture = _load_cached_texture(asset_type)
				if texture:
					asset_cache[asset_type] = texture
					print("Loaded cached asset: ", asset_type)
		
		file_name = dir.get_next()
	
	print("Loaded ", asset_cache.size(), " cached assets")

func _load_cached_texture(asset_type: String) -> ImageTexture:
	var path = CACHE_DIR + asset_type + ".png"
	
	if not FileAccess.file_exists(path):
		return null
	
	var image = Image.new()
	var error = image.load(path)
	
	if error != OK:
		print("Failed to load cached image: ", path)
		return null
	
	var texture = ImageTexture.new()
	texture.create_from_image(image)
	return texture

func _load_fallback_asset(asset_type: String) -> ImageTexture:
	var fallback_path = "res://fallback_assets/" + asset_type + ".png"
	
	if ResourceLoader.exists(fallback_path):
		return load(fallback_path)
	
	# Try generic fallbacks
	var generic_fallbacks = {
		"button_normal": "res://fallback_assets/default_button.png",
		"button_pressed": "res://fallback_assets/default_button.png",
		"main_background": "res://fallback_assets/default_bg.png",
		"loading_spinner": "res://fallback_assets/loading_icon.png"
	}
	
	if generic_fallbacks.has(asset_type):
		var generic_path = generic_fallbacks[asset_type]
		if ResourceLoader.exists(generic_path):
			return load(generic_path)
	
	return null

func _create_placeholder_texture(asset_type: String) -> ImageTexture:
	var specs = MOBILE_ASSET_SPECS.get(asset_type, {"width": 128, "height": 128})
	var image = Image.create(specs.width, specs.height, false, Image.FORMAT_RGBA8)
	
	# Create a simple colored placeholder
	var color = Color.DARK_GRAY
	match asset_type:
		"main_background":
			color = Color(0.1, 0.1, 0.15, 1.0)  # Dark blue-gray
		"button_normal", "button_pressed":
			color = Color(0.3, 0.25, 0.2, 1.0)  # Brown-gray
		"menu_frame":
			color = Color(0.2, 0.15, 0.1, 0.8)  # Transparent brown
		_:
			color = Color.GRAY
	
	image.fill(color)
	
	var texture = ImageTexture.new()
	texture.create_from_image(image)
	return texture

func clear_cache():
	"""Clear all cached assets"""
	var dir = DirAccess.open(CACHE_DIR)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if file_name.ends_with(".png"):
				dir.remove(file_name)
				print("Removed cached file: ", file_name)
			file_name = dir.get_next()
	
	asset_cache.clear()
	print("Asset cache cleared")

func get_cache_size() -> int:
	"""Get number of cached assets"""
	return asset_cache.size()

func is_asset_cached(asset_type: String) -> bool:
	"""Check if asset is cached"""
	return asset_cache.has(asset_type)
